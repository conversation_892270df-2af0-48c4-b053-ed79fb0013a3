<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email de Vérification Envoyé - Notif <PERSON></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
            position: relative;
        }

        /* Header */
        .header {
            position: relative;
            z-index: 10;
            padding: 1rem 2rem;
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-icon i {
            font-size: 1.2rem;
            color: white;
        }

        .back-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Main Container */
        .main-container {
            position: relative;
            z-index: 5;
            min-height: calc(100vh - 80px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        /* Confirmation Card */
        .confirmation-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 3rem;
            max-width: 600px;
            width: 100%;
            text-align: center;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            animation: slideUp 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }

        .confirmation-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
            pointer-events: none;
        }

        .confirmation-card > * {
            position: relative;
            z-index: 2;
        }

        /* Success Icon */
        .success-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            animation: bounceIn 1s ease-out 0.3s both;
            box-shadow: 0 20px 40px rgba(16, 185, 129, 0.3);
        }

        .success-icon i {
            font-size: 3rem;
            color: white;
        }

        /* Content */
        .confirmation-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: fadeInUp 0.8s ease-out 0.5s both;
        }

        .confirmation-subtitle {
            font-size: 1.25rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
            line-height: 1.6;
            animation: fadeInUp 0.8s ease-out 0.7s both;
        }

        .user-email {
            color: #ff72b6;
            font-weight: 600;
            background: rgba(79, 70, 229, 0.1);
            padding: 0.25rem 0.75rem;
            border-radius: 8px;
            border: 1px solid rgba(79, 70, 229, 0.3);
        }

        /* Instructions */
        .instructions {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            margin: 2rem 0;
            animation: fadeInUp 0.8s ease-out 0.9s both;
        }

        .instructions-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }

        .instructions-list {
            list-style: none;
            text-align: left;
            max-width: 400px;
            margin: 0 auto;
        }

        .instructions-list li {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
        }

        .step-number {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
            flex-shrink: 0;
            margin-top: 0.125rem;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
            animation: fadeInUp 0.8s ease-out 1.1s both;
        }

        .btn {
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            font-size: 1rem;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(79, 70, 229, 0.4);
        }

        /* Animations */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }

            .header-container {
                flex-direction: column;
                gap: 1rem;
            }

            .confirmation-card {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }

            .confirmation-title {
                font-size: 2rem;
            }

            .confirmation-subtitle {
                font-size: 1.125rem;
            }

            .success-icon {
                width: 100px;
                height: 100px;
            }

            .success-icon i {
                font-size: 2.5rem;
            }

            .action-buttons {
                flex-direction: column;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                padding: 1rem;
            }

            .confirmation-card {
                padding: 1.5rem 1rem;
            }

            .instructions {
                padding: 1.5rem;
            }

            .instructions-list {
                max-width: 100%;
            }
        }
        .logo-icon {
            width: 45px;
            height: 45px;
            background: #2563eb;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            }
  
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="#" class="logo">
                 <div class="logo-icon animate-pulse">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                        </svg>
                </div>
                Notif Hub
            </a>
            <a href="{{route('loginPage')}}" class="back-button">
                <i class="fas fa-arrow-left"></i>
                Log In Page
            </a>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">
        <div class="confirmation-card">
            <!-- Success Icon -->
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>

            <!-- Title and Subtitle -->
            <h1 class="confirmation-title">
                Email de vérification envoyé ! 📧
            </h1>
            
            <p class="confirmation-subtitle">
                Nous avons envoyé un email de vérification à<br>
                <span class="user-email" id="userEmail">{{email}}</span>
            </p>

            <!-- Instructions -->
            <div class="instructions">
                <h2 class="instructions-title">
                    <i class="fas fa-list-check"></i>
                    Étapes suivantes
                </h2>
                <ol class="instructions-list">
                    <li>
                        <span class="step-number">1</span>
                        <span>Ouvrez votre boîte de réception Gmail</span>
                    </li>
                    <li>
                        <span class="step-number">2</span>
                        <span>Recherchez l'email de Notif Hub (vérifiez aussi les spams)</span>
                    </li>
                    <li>
                        <span class="step-number">3</span>
                        <span>Cliquez sur le bouton "Vérifier mon email"</span>
                    </li>
                    <li>
                        <span class="step-number">4</span>
                        <span>Vous serez automatiquement connecté à votre compte</span>
                    </li>
                </ol>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="https://gmail.com" target="_blank" class="btn btn-primary">
                    <i class="fab fa-google"></i>
                    Ouvrir Gmail
                </a>
            </div>
        </div>
    </main>
</body>
</html>