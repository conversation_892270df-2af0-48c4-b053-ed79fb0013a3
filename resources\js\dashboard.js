// Global variables
let projects = []
let currentFilters = {
  search: '',
}
let currentViewMode = 'grid'
window.OpenNotifictions = OpenNotifictions
// Initialize the page
document.addEventListener('DOMContentLoaded', function () {
  fetch('/main/dashboard/projects')
    .then((res) => res.json())
    .then((data) => {
      projects = data // store in the global variable
      renderProjects() // now render them
      let notificationsNumber = 0
      projects.forEach((projet) => {
        notificationsNumber += projet.notificationsNumber
      })
      document.getElementById('totalNotifications').innerText = notificationsNumber.toLocaleString()
      document.getElementById('moyenne').innerText = Math.ceil(notificationsNumber/projects.length)|| 0
    })
    .catch((err) => {
      console.error('Error fetching projects:', err)
    })
  setupEventListeners()
  
})

// Setup event listeners
function setupEventListeners() {
  // Search input
  const searchInput = document.getElementById('searchInput')
  if (searchInput) {
    searchInput.addEventListener('input', function (e) {
      currentFilters.search = e.target.value
      renderProjects()
    })
  }
  // Close modals when clicking outside
  document.addEventListener('click', function (e) {
    if (e.target.classList.contains('modal')) {
      closeAllModals()
    }
  })

  // Context menu
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault()
  })

  document.addEventListener('click', function (e) {
    const contextMenu = document.getElementById('contextMenu')
    if (contextMenu && !e.target.closest('.context-menu')) {
      contextMenu.classList.remove('show')
    }
  })
}
// Filter projects
function filterProjects() {
  return projects.filter((project) => {
    const matchesSearch =
      project.name.toLowerCase().includes(currentFilters.search.toLowerCase()) ||
      project.description.toLowerCase().includes(currentFilters.search.toLowerCase())

    return matchesSearch
  })
}
// Render projects
function renderProjects() {
  const filteredProjects = filterProjects()
  const container = document.getElementById('projectsContainer')
  const emptyState = document.getElementById('emptyState')

  if (!container) return

  if (filteredProjects.length === 0) {
    container.style.display = 'none'
    if (emptyState) emptyState.style.display = 'block'
    return
  }

  container.style.display = currentViewMode === 'grid' ? 'grid' : 'flex'
  container.className = currentViewMode === 'grid' ? 'projects-grid' : 'projects-list'
  if (emptyState) emptyState.style.display = 'none'

  container.innerHTML = filteredProjects
    .map((project) => {
      if (currentViewMode === 'grid') {
        return createProjectCard(project)
      } else {
        return createProjectListItem(project)
      }
    })
    .join('')
}
// Create project card (grid view)
function createProjectCard(project) {
  const createdDate = new Date(project.createdAt).toLocaleDateString('fr-FR')
  const updatedDate = new Date(project.updatedAt).toLocaleDateString('fr-FR')

  return `
        <div class="project-card fade-in" onclick='OpenNotifictions(${project.id})'>
            <div class="project-header">
                <div class="project-info">
                    <div class="project-icon">
                        <i class="fas fa-bell" style="color: #8b5cf6;"></i>
                    </div>
                    <div style="flex: 1; min-width: 0;">
                        <h3 class="project-name">${project.name}</h3>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="more-btn" title="Modider le projet" onclick='event.stopPropagation();OpenEditModal(${JSON.stringify(project).replace(/'/g, '&apos;')})'>
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="more-btn deletebtn" title="Supprimer le projet" onclick='event.stopPropagation();OpenDeleteModal(${JSON.stringify(project).replace(/'/g, '&apos;')})'>
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <p class="project-description">${project.description ?? ''}</p>
            <div class="project-stats">
                <div class="project-stat">
                    <p class="project-stat-label">Notifications</p>
                    <p class="project-stat-value">${project.notificationsNumber.toLocaleString()}</p>
                </div>
                <div class="project-stat">
                    <p class="project-stat-label">Créé le</p>
                    <p class="project-stat-value">${createdDate}</p>
                </div>
                <div class="project-stat">
                    <p class="project-stat-label">Mis à jour</p>
                    <p class="project-stat-value">${updatedDate}</p>
                </div>
            </div>
        </div>
    `
}
// Create project list item (list view)
function createProjectListItem(project) {
  const createdDate = new Date(project.createdAt).toLocaleDateString('fr-FR')
  const updatedDate = new Date(project.updatedAt).toLocaleDateString('fr-FR')

  return `
        <div class="project-card fade-in" onclick='OpenNotifictions(${project.id})'>
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center; gap: 1rem; flex: 1;">
                    <div class="project-icon">
                        <i class="fas fa-bell" style="color: #8b5cf6;"></i>
                    </div>
                    <div style="flex: 1; min-width: 0;">
                        <h4 style="font-size: 1.125rem; font-weight: 600; margin: 0 0 0.25rem 0;">${project.name}</h4>
                        <p style="color: rgba(255, 255, 255, 0.7); font-size: 0.875rem; margin-bottom: 0.5rem;">${project.description ?? ''}</p>
                        <div style="display: flex; align-items: center; gap: 1.5rem; font-size: 0.875rem;">
                            <span style="color: rgba(255, 255, 255, 0.4);">Notifications: <span style="color: white;">${project.notificationsNumber.toLocaleString()}</span></span>
                            <span style="color: rgba(255, 255, 255, 0.4);">Créé: <span style="color: white;">${createdDate}</span></span>
                            <span style="color: rgba(255, 255, 255, 0.4);">Mis à jour: <span style="color: white;">${updatedDate}</span></span>
                        </div>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="more-btn" title="Modider le projet" onclick='event.stopPropagation();OpenEditModal(${JSON.stringify(project).replace(/'/g, '&apos;')})'>
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="more-btn deletebtn" title="Supprimer le projet" onclick='event.stopPropagation();OpenDeleteModal(${JSON.stringify(project).replace(/'/g, '&apos;')})'>
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `
}
document.addEventListener('DOMContentLoaded', () => {
  document.getElementById('gridView')?.addEventListener('click', () => setViewMode('grid'))
  document.getElementById('listView')?.addEventListener('click', () => setViewMode('list'))
})
function setViewMode(mode) {
  currentViewMode = mode
  // Update button states
  const gridViewBtn = document.getElementById('gridView')
  const listViewBtn = document.getElementById('listView')

  if (gridViewBtn) gridViewBtn.classList.toggle('active', mode === 'grid')
  if (listViewBtn) listViewBtn.classList.toggle('active', mode === 'list')

  renderProjects()
}
function OpenNotifictions(projectId) {
  window.location.href = `/main/dashboard/projects/${projectId}/notifications`
}
function closeAllModals() {
  const modals = document.querySelectorAll('.modal')
  modals.forEach((modal) => modal.classList.remove('show'))
}

