import projet from '#models/projet'
import Notification from '#models/notification'
import type { HttpContext } from '@adonisjs/core/http'

export default class ProjectsController {
  public async index({ response, auth, view }: HttpContext) {
    const user = auth.user
    if (!user) {
      return view.render('dashboardPage', { projects: [] })
    }
    const projects = await projet.query().where('userId', user.id).orderBy('created_at', 'desc')
    return response.json(projects)
  }

  async store({ request, auth, response ,session}: HttpContext) {
    const data = request.only(['name', 'description'])

    session.flash('success', 'Projet créé avec succès ✅')
    await projet.create({
      ...data,
      userId: auth.user!.id,
    })
    if (auth.user)
    {
      auth.user.projectsNumber += 1
      await auth.user.save()  
    }

    return response.redirect().toRoute('dashboardPage')
  }
  async update({ request, params ,session}: HttpContext) {
    const project = await projet.findOrFail(params.id)
    const data = request.only(['name', 'description'])
    project.name = data.name
    project.description = data.description
    await project.save()
    return session.flash('success', 'Projet Modifié avec succès ✅')
  }
  async destroy({ params ,session , auth}: HttpContext) {
    const project = await projet.findOrFail(params.id)
    await project.delete()
    if (auth.user) {
      auth.user.projectsNumber -= 1
      await auth.user.save()
    }
    return session.flash('success', 'Projet supprimé avec succès ✅')
  }
  public async notificationsPage({ params, auth, response, view }: HttpContext) {
    const project = await projet.findOrFail(params.id)

 
    if (project.userId !== auth.user!.id) {
      return response.unauthorized({ error: 'You are not allowed to view this project' })
    }
    const notifications = await Notification.query().where('project_id', project.id).orderBy('created_at', 'desc')
    return view.render('pages/notificationsPage/index', {
      project,
      notifications
    })
  }
}