<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <title>title</title>
    @vite('resources/js/app.js')
    @vite('resources/css/HomePage.css')
</head>
<body>
    <div id="register-view" class="view auth-view">
        <div class="auth-container">
            <div class="auth-card animate-scale-in">
                <div class="auth-header">
                    <div class="auth-nav">
                        <a href="{{ route('home') }}" class="btn-back">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M19 12H5M12 19l-7-7 7-7"/>
                            </svg>
                            Back
                        </a>
                        <div class="auth-logo">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                            </svg>
                        </div>
                    </div>
                    <h2 class="auth-title">Create Account</h2>
                    <p class="auth-description">Join Notif Hub and start sending notifications</p>
                </div>
                <div class="auth-content">
                    <form id="registerForm" class="auth-form" action="{{ route('auth.register') }}" method="POST">
                        {{ csrfField() }}
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" name="email" id="email" value="{{ old('email') || '' }}" placeholder="Enter your email" required>
                            @inputError('email')
                                          <p class="text-red-500 text-sm mt-2">{{ $messages.join(', ') }}</p>
                            @end
                        </div>
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" name="password" id="password" placeholder="Create a password" required>
                            @inputError('password')
                                <p class="text-red-500 text-sm mt-2">{{ $messages.join(', ') }}</p>
                            @end
                        </div>
                        <div class="form-group">
                            <label for="confirm-password">Confirm Password</label>
                            <input type="password" name="confirm-password" id="confirm-password" placeholder="Confirm your password" required>
                            <p id="error-message" class="text-red-500 text-sm hidden">Passwords do not match</p>
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">Create Account</button>
                    </form>
                    <script>
                                document.addEventListener('DOMContentLoaded', () => {
                                const form = document.getElementById('registerForm');
                                const passwordInput = document.getElementById('password');
                                const confirmPasswordInput = document.getElementById('confirm-password');
                                const errorMsg = document.getElementById('error-message');

                                if (!form || !passwordInput || !confirmPasswordInput) return;

                                form.addEventListener('submit', function (e) {
                                    const password = passwordInput.value;
                                    const confirm = confirmPasswordInput.value;

                                    if (password !== confirm) {
                                    e.preventDefault(); // Stop form submission
                                    errorMsg.classList.remove('hidden');
                                    } else {
                                    errorMsg.classList.add('hidden');
                                    }
                                });
                                });

                    </script>
                    <div class="auth-switch">
                        <a href="{{ route('loginPage') }}" class="link-button">
                            Already have an account? Sign in here
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
