<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrfToken }}">
    <title>Gestion des Projets - Notif Hub</title>
     @vite('resources/css/dashboard.css')
     @vite('resources/js/dashboard.js')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        function showToast(message) {
                const toast = document.createElement('div')
                toast.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                        color: white;
                        padding: 1rem 1.5rem;
                        border-radius: 12px;
                        font-weight: 500;
                        z-index: 1000;
                        animation: slideInRight 0.3s ease-out;
                        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
                    `
                toast.textContent = message

                // Add animation keyframes if not already added
                if (!document.getElementById('toast-styles')) {
                    const style = document.createElement('style')
                    style.id = 'toast-styles'
                    style.textContent = `
                            @keyframes slideInRight {
                                from {
                                    opacity: 0;
                                    transform: translateX(100%);
                                }
                                to {
                                    opacity: 1;
                                    transform: translateX(0);
                                }
                            }
                            @keyframes slideOutRight {
                                from {
                                    opacity: 1;
                                    transform: translateX(0);
                                }
                                to {
                                    opacity: 0;
                                    transform: translateX(100%);
                                }
                            }
                        `
                    document.head.appendChild(style)
                }

                document.body.appendChild(toast)

                setTimeout(() => {
                    toast.style.animation = 'slideOutRight 0.3s ease-out'
                    setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast)
                    }
                    }, 300)
                }, 4000)
        }
    </script>
<body>
    <!-- Header -->
    @component('./pages/DashboardComponents/header', { email: auth.user.email , isAdmin: auth.user.isAdmin}) 
    @end
    <div class="main-container">
        <!-- Stats Overview -->
        @component('./pages/DashboardComponents/StatsOverview', { projectsNumber: auth.user.projectsNumber })   
        @end
        <!-- Filters and Search -->
        @component('./pages/DashboardComponents/SearchSection', {  })   
        @end
        <!-- Projects Display -->
        @component('./pages/DashboardComponents/ProjectsDisplay', {  }) 
        @end
        <!-- Empty State -->
        <div id="emptyState" class="empty-state" style="display: none;">
            <div class="empty-icon">
                <i class="fas fa-search"></i>
            </div>
            <h3>Aucun projet trouvé</h3>
            <p>Essayez de modifier vos critères de recherche ou créez un nouveau projet.</p>
        </div>
    </div>

    <!-- Create Project Modal -->
    @component('./pages/DashboardComponents/createProjectModal')
     @end
    <!-- EDIT Project Modal -->
    @component('./pages/DashboardComponents/EditProjectModal')
     @end
    <!-- Project Delete Modal -->
    @component('./pages/DashboardComponents/ProjectDeleteModal', {  })     
    @end
</body>
</html>