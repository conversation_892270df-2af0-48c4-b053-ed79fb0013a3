// start/env.ts

import { Env } from '@adonisjs/core/env'

export default await Env.create(new URL('../', import.meta.url), {
  NODE_ENV: Env.schema.enum(['development', 'production', 'test'] as const),
  PORT: Env.schema.number(),
  APP_KEY: Env.schema.string(),
  HOST: Env.schema.string({ format: 'host' }),
  LOG_LEVEL: Env.schema.string(),

  /*
  |--------------------------------------------------------------------------
  | Variables for configuring session package
  |--------------------------------------------------------------------------
  */
  SESSION_DRIVER: Env.schema.enum(['cookie', 'memory'] as const),

  /*
  |--------------------------------------------------------------------------
  | Variables for configuring ally package
  |--------------------------------------------------------------------------
  */
  GOOGLE_CLIENT_ID: Env.schema.string(),
  GOOGLE_CLIENT_SECRET: Env.schema.string(),

  /*
  |--------------------------------------------------------------------------
  | Variables for configuring the mail package
  |--------------------------------------------------------------------------
  */
  MAIL_HOST: Env.schema.string(),
  MAIL_PORT: Env.schema.number(),
  MAIL_USERNAME: Env.schema.string(),
  MAIL_PASSWORD: Env.schema.string(),
  MAIL_ENCRYPTION: Env.schema.string.optional(), // TLS, SSL, etc.
})
