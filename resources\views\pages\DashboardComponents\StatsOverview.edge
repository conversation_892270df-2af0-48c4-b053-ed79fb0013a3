<div class="stats-grid">
            <div class="stat-card">
                <div class="stat-content">
                    <div class="stat-info">
                        <p class="stat-label">Total Projets</p>
                        <p class="stat-value" id="totalProjects">
                             @if(projectsNumber > 0)
                                {{ projectsNumber }}
                            @else
                                0
                            @endif
                        </p>
                    </div>
                    <i class="fas fa-cog stat-icon blue"></i>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-content">
                    <div class="stat-info">
                        <p class="stat-label">Total Notifications</p>
                        <p class="stat-value" id="totalNotifications"></p>
                    </div>
                    <i class="fas fa-paper-plane stat-icon purple"></i>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-content">
                    <div class="stat-info">
                        <p class="stat-label"> Notifications / Projet (moyenne)</p>
                        <p class="stat-value" id="moyenne"></p>
                    </div>
                    <i class="fas fa-chart-line stat-icon green"></i>
                </div>
            </div>

        </div>