<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notif Hub - Powerful Notifications API</title>
    @vite('resources/js/app.js')
    @vite('resources/css/HomePage.css')

</head>
<body">
    <div id="landing-view" class="view active">
        <div class="container">
            <!-- Header -->
            <header class="header animate-fade-in">
                <div class="logo-section">
                    <div class="logo-icon animate-pulse">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                        </svg>
                    </div>
                    <div class="logo-text">
                        <h1>Welcome to Notif Hub</h1>
                        <p>Your one-stop solution for SMS and Email notifications.</p>
                    </div>
                </div>

                <!-- Auth Buttons -->
                <div class="auth-buttons animate-slide-in-right">
                    <a href="{{ route('loginPage') }}" class="btn btn-outline">Sign In</a>
                    <a href="{{ route('auth.register') }}" class="btn btn-primary">Register</a>
                </div>
            </header>

            <!-- Main Content -->
            <main class="main-content">
                <div class="content-left animate-slide-in-left">
                    <div class="hero-text">
                        <h2 class="hero-title">
                            Powerful Notifications<br>
                            API <span class="text-blue animate-pulse">to Reach Your</span><br>
                            <span class="text-purple">Users Instantly!</span>
                        </h2>

                        <p class="hero-description">
                            Simplify how you send SMS and Email notifications.<br>
                            Choose your provider, design your templates, and
                            integrate easily with our powerful API made for developers.
                        </p>
                    </div>

                    <div class="hero-buttons">
                        <a href="{{ route('registerPage') }}" class="btn btn-primary btn-large">
                            Try Notif Hub
                            <svg class="arrow-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M5 12h14M12 5l7 7-7 7"/>
                            </svg>
                        </a>
                        <a href="{{ route('LearnMorePage')}}" class="btn btn-outline btn-large">Learn More</a>
                    </div>
                </div>

                <!-- Illustration -->
                <div class="content-right animate-slide-in-right">
                    <div class="illustration-container">
                        <div class="illustration-card animate-float">
                            <div class="icon-container">
                                <div class="icon animate-bounce-slow">
                                    <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                        <polyline points="22,6 12,13 2,6"/>
                                    </svg>
                                </div>
                                <div class="icon animate-bounce-slow delay-300">
                                    <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>