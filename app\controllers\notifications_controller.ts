import Notification from '#models/notification'
import Projet from '#models/projet'
import Template from '#models/template'
import { HttpContext } from '@adonisjs/core/http'
export default class NotificationsController {
  public async store({ request, session }: HttpContext) {

      const data = request.only([
        'projectId',
        'title',
        'type',
        'expediteur',
        'appPassword',
        'template',
      ])
      const project = await Projet.findOrFail(data.projectId)
      if (project) {
        project.notificationsNumber += 1
        await project.save()
      }

      const template = await Template.create({
        html: data.template,
      })

      const notification = await Notification.create({
        projectId: data.projectId,
        title: data.title,
        type: data.type,
        expediteur: data.expediteur,
        appPassword: data.appPassword,
        templateId: template.id,
      })
      const regex = /{{\s*(\w+)\s*}}/g
      const variables = []
      let match
      while ((match = regex.exec(data.template)) !== null) {
        variables.push(match[1])
      }
      const jsonContent: Record<string, any> = {
        destinateur: '',
        apiKey: notification.apiKey,
      }

      variables.forEach((v) => {
        jsonContent[v] = ''
      })

      notification.jsonContent = JSON.stringify(jsonContent)
      await notification.save()

      return session.flash('success', 'Projet Modifié avec succès ✅')

  }

  async destroy({ params, session }: HttpContext) { 
    const notification = await Notification.findOrFail(params.id)
    const project = await Projet.findOrFail(notification.projectId)
    if (project) {
      project.notificationsNumber -= 1
      await project.save()
    }
    await notification.delete()
    return session.flash('success', 'Projet supprimé avec succès ✅')
  }
  async getTemplate({ params, response }: HttpContext) {
    const template = await Template.findOrFail(params.id)
    return response.ok({ template })
  }

  public async update({ params, request, response }: HttpContext) {
    try {
      // Récupération de la notification par ID
      const notification = await Notification.findOrFail(params.id)

      // Récupération des données envoyées
      const data = request.only(['title', 'type', 'expediteur', 'appPassword', 'template'])

      // Mise à jour des champs simples
      notification.merge({
        title: data.title ?? notification.title,
        type: data.type ?? notification.type,
        expediteur: data.expediteur ?? notification.expediteur,
        appPassword: data.appPassword ?? notification.appPassword,
      })

      // Vérifier si un template est fourni et mettre à jour
      if (data.template) {
        const template = await Template.findOrFail(notification.templateId)

        // Mettre à jour le HTML du template
        template.html = data.template
        await template.save()

        // Extraire les variables dynamiques {{ variable }}
        const regex = /{{\s*(\w+)\s*}}/g
        const variables: string[] = []
        let match
        while ((match = regex.exec(data.template)) !== null) {
          variables.push(match[1])
        }

        // Recréer le jsonContent
        const jsonContent: Record<string, any> = {
          destinateur: '',
          apiKey: notification.apiKey,
        }
        variables.forEach((v) => {
          jsonContent[v] = ''
        })

        notification.jsonContent = JSON.stringify(jsonContent)
      }

      // Sauvegarde finale
      await notification.save()

      return response.ok({
        message: 'Notification updated successfully',
        notification,
      })
    } catch (error) {
      console.error(error)
      return response.badRequest({
        message: 'Error while updating notification',
        error: error.message,
      })
    }
  }
}
