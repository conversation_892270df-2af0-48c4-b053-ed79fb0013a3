<div id="EditProjectModal" class="modal">
        <div class="modal-content">
            <div class="modal-header" align="center">
                <h2>Modifier le Projet : <span id="editProjectName" style="color:#2563eb;font-weight:bolder;font-size: larger;"></span></h2>
                <button class="modal-close" onclick="closeEditProject()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                            <label for="name">Nom du Projet <span style="color: rgb(255, 114, 114); font-size: large;">*</span></label>
                            <input type="text" id="Editname" name="name" placeholder="Mon Nouveau Projet" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="Editdescription" name="description" placeholder="Décrivez l'objectif et l'utilisation de ce projet..." rows="3"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-primary" id="EditProjectBtn">
                            <i class="fas fa-edit"></i>
                            Modifier le Projet
                        </button>
                        <button type="button" class="btn-outline" onclick="closeEditProject()">
                            Annuler
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
       let currentProjectToEdit= null;
       window.OpenEditModal = OpenEditModal
       function closeEditProject() {
            const modal = document.getElementById('EditProjectModal');
            if (modal) modal.classList.remove('show');

            // Réinitialiser manuellement les champs au lieu de reset()
            const nameElem = document.getElementById('Editname');
            const descElem = document.getElementById('Editdescription');
            if (nameElem) nameElem.value = '';
            if (descElem) descElem.value = '';

            // Si tu veux, tu peux aussi reset currentProjectToEdit
            currentProjectToEdit = null;
        }
       function OpenEditModal(project) {
                if (typeof project === 'string') {
                    project = JSON.parse(project)
                }
                currentProjectToEdit = project.id;
                const editProjectNameElem = document.getElementById('editProjectName');
                const nameElem = document.getElementById('Editname')
                const descElem = document.getElementById('Editdescription')
                if (editProjectNameElem) {
                    editProjectNameElem.textContent = project.name;
                }
                if (nameElem)
                    nameElem.value = project.name
                if (descElem) 
                    descElem.value = project.description  
                const modal = document.getElementById('EditProjectModal')
                if (modal) {
                    modal.classList.add('show')
                }
        }
       document.getElementById('EditProjectBtn').addEventListener('click', (event) => {
            event.preventDefault();
            if (!currentProjectToEdit) return;
            const updatedData = {
                name: document.getElementById('Editname').value,
                description: document.getElementById('Editdescription').value || ""
            };

            fetch(`/main/dashboard/projects/${currentProjectToEdit}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(updatedData)
            })
            .then(res => {
                    if (res.ok) {
                        closeEditProject();
                        showToast("Projet modifié avec succès ✅");
                        location.reload();
                    } else {
                        showToast("Erreur lors de la modification ❌");
                    }
                })
            .catch(err => console.error(err));
        }); 
    </script>