/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%);
    color: white;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Header Styles */
.header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(12px);
    position: sticky;
    top: 0;
    z-index: 40;
}

.header-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-divider {
    width: 1px;
    height: 1.5rem;
    background: rgba(255, 255, 255, 0.2);
}

.header-title h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.header-title p {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Button Styles */
.btn-primary {
    background: #2563eb;
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
}

.btn-outline {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.2);
}

.btn-ghost {
    background: transparent;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-ghost:hover {
    background: rgba(255, 255, 255, 0.1);
}
.logout-btn:hover {
    color: rgb(255, 72, 72);
    
   
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
}

/* Main Container */
.main-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 2rem 1.5rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.stat-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stat-label {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.875rem;
    font-weight: 700;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-icon.blue {
    background: rgba(59, 130, 246, 0.2);
    color: #60a5fa;
}

.stat-icon.green {
    background: rgba(34, 197, 94, 0.2);
    color: #4ade80;
}

.stat-icon.purple {
    background: rgba(168, 85, 247, 0.2);
    color: #a78bfa;
}

.stat-icon.yellow {
    background: rgba(245, 158, 11, 0.2);
    color: #fbbf24;
}

/* Filters Container */
.filters-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

@media (min-width: 1024px) {
    .filters-container {
        flex-direction: row;
        align-items: center;
    }
}

.search-container {
    position: relative;
    flex: 1;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.4);
}

.search-input {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: white;
    font-size: 0.875rem;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
}

.filters-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
}

.dropdown-trigger {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #1e293b;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
    z-index: 50;
    display: none;
    min-width: 200px;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
    color: rgba(255, 255, 255, 0.7);
}

.dropdown-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

/* View Toggle */
.view-toggle {
    display: flex;
    height: 2.7rem;
    width: 8rem;
    border: 3px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
}

.view-btn {
    background: transparent;
    border: none;
    border-radius: 0.2rem;
    width: 4rem;
    color: white;
    cursor: pointer;
    transition: all 0.2s;
}

.view-btn.active {
    background: #2563eb;
}

/* Projects Grid */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.projects-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Project Card */
.project-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.project-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.project-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.project-info {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    flex: 1;
}

.project-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.project-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.project-name {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
}

.star-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    padding: 0.25rem;
    transition: color 0.2s;
}

.star-btn.starred {
    color: #fbbf24;
}

.project-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.project-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.project-stat {
    display: flex;
    flex-direction: column;
}

.project-stat-label {
    color: rgba(255, 255, 255, 0.4);
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

.project-stat-value {
    color: white;
    font-weight: 500;
    font-size: 0.875rem;
}

.project-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.4);
}

.project-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    opacity: 0;
    transition: opacity 0.3s;
}

.project-card:hover .project-actions {
    opacity: 1;
}

.more-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s;
}

.more-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}
.deletebtn:hover{
    background: rgba(255, 255, 255, 0.1);
    color: rgb(255, 51, 51);
}

/* Badge Styles */
.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.badge.active {
    background: #10b981;
    color: white;
}

.badge.inactive {
    background: #6b7280;
    color: white;
}

.badge.paused {
    background: #f59e0b;
    color: white;
}

.badge.sent {
    background: #10b981;
    color: white;
}

.badge.pending {
    background: #f59e0b;
    color: white;
}

.badge.failed {
    background: #ef4444;
    color: white;
}

.badge.scheduled {
    background: #3b82f6;
    color: white;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-icon {
    width: 6rem;
    height: 6rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 3rem;
    color: rgba(255, 255, 255, 0.4);
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 100;
    padding: 1rem;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: #1e293b;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.75rem;
    max-width: 32rem;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

.modal-content.large {
    max-width: 64rem;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.modal-header p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.modal-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

@media (min-width: 768px) {
    .form-row {
        grid-template-columns: 1fr 1fr;
    }
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: white;
    font-size: 0.875rem;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
}

.form-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.form-checkbox input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
}

.form-checkbox label {
    font-size: 0.875rem;
    margin: 0;
}

.form-actions {
    display: flex;
    gap: 0.75rem;
    padding-top: 1rem;
}

.form-actions .btn-primary,
.form-actions .btn-outline {
    flex: 1;
}

/* Project Details Modal */
.project-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.project-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.detail-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.detail-stat {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 0.5rem;
}

.detail-stat-label {
    color: rgba(255, 255, 255, 0.4);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.detail-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
}

.detail-stat-value.success {
    color: #4ade80;
}

.detail-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
    .detail-content {
        grid-template-columns: 1fr 1fr;
    }
}

.detail-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.detail-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-row span:first-child {
    color: rgba(255, 255, 255, 0.4);
}

.api-key-container {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 0.5rem;
}

.api-key-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.api-key-header span {
    color: rgba(255, 255, 255, 0.4);
    font-size: 0.875rem;
}

#detailApiKey {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    word-break: break-all;
}

.notifications-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.notification-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.notification-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-status {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.detail-actions {
    display: flex;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: #1e293b;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
    z-index: 1000;
    display: none;
    min-width: 180px;
}

.context-menu.show {
    display: block;
}

.context-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.2s;
}

.context-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.context-item.danger {
    color: #ef4444;
}

.context-item.danger:hover {
    background: rgba(239, 68, 68, 0.2);
}

.context-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 0.5rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .header-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .filters-container {
        flex-direction: column;
    }

    .filters-actions {
        flex-wrap: wrap;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 1rem;
        max-width: none;
    }

    .detail-actions {
        flex-direction: column;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}