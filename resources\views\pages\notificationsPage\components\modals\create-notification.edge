        <head>
            <link href="https://unpkg.com/grapesjs/dist/css/grapes.min.css" rel="stylesheet"/>
        </head>
        <div id="createNotificationModal" class="modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h2>Créer une Nouvelle Notification</h2>
                    <p>Configurez votre notification avec tous les détails nécessaires.</p>
                    <button class="modal-close" onclick="closeCreateNotification()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="createNotificationForm">
                        {{ csrfField() }}
                        <div class="form-row">
                            <div class="form-group">
                                <label for="notificationTitle">Titre de la Notification *</label>
                                <input type="text" id="notificationTitle" required>
                            </div>
                            <div class="form-group">
                                <label for="notificationType">Type de Notification *</label>
                                <select id="notificationType" required onchange="updateTypeFields()">
                                    <option value="email" style="color: black;">Email</option>
                                    <option value="sms" style="color: black;">SMS</option>
                                    <option value="push" style="color: black;">Push Notification</option>
                                </select>
                            </div>
                        </div>
                        <label for="notificationTemplate">Template *</label>
                        <div id="gjs"></div>
                        <br><br>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="notificationEmail">Email expéditeur *</label>
                                <input type="email" id="notificationEmail" required>
                            </div>
                            <div class="form-group">
                                <label for="notificationPassword">Mot de passe d’application (généré depuis votre compte Google expéditeur) *</label>
                                <input type="password" id="notificationPassword" required>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-primary" style="width:11rem;">
                                <i class="fas fa-paper-plane"></i>
                                Créer la notification
                            </button>
                            <button type="button" class="btn-outline" style="width:8rem;border-radius: 0.5rem;" onclick="closeCreateNotification()">
                                Annuler
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <script src="https://unpkg.com/grapesjs"></script>
        <script src="https://unpkg.com/grapesjs-preset-newsletter"></script>
        <script src="https://unpkg.com/grapesjs-blocks-basic"></script>
        <script>
            const editor = grapesjs.init({
                container: '#gjs',
                height: '100vh',
                fromElement: false,
                storageManager: false,
                plugins: ['gjs-preset-newsletter', 'gjs-blocks-basic'],
                pluginsOpts: {
                    'gjs-preset-newsletter': {
                        modalTitleImport: 'Import template',
                        modalTitleExport: 'Export template',
                        codeViewerTheme: 'material',
                    }
                }
            });
            editor.BlockManager.add('merge-var', {
                label: 'Variable',
                category: 'Basic',
                attributes: { class: 'fa fa-code', title: 'Variable Block' },
                content:'<span>&#123;&#123; variableName &#125;&#125;</span>'
            });
            function openCreateNotification(){
                document.getElementById('createNotificationModal').classList.add('show');
                setTimeout(() => editor.refresh(), 100);         
            }
            function closeCreateNotification() {
                document.getElementById('createNotificationModal').classList.remove('show');
                // vider manuellement les champs du formulaire
                document.getElementById('notificationTitle').value = '';
                document.getElementById('notificationType').value = 'email';
                document.getElementById('notificationEmail').value = '';
                document.getElementById('notificationPassword').value = '';

                // vider GrapesJS
                editor.setComponents('');
                editor.setStyle('');
            }



            document.getElementById('createNotificationForm').addEventListener('submit', async (e) => {
                e.preventDefault();

                const title = document.getElementById('notificationTitle').value;
                const type = document.getElementById('notificationType').value;
                const senderEmail = document.getElementById('notificationEmail').value;
                const appPassword = document.getElementById('notificationPassword').value;

                const projectId = {{ project.id }};

                const templateHtml = editor.getHtml();
                const templateCss  = editor.getCss();
                const finalTemplate = `
                <html>
                    <head>
                    <style>${templateCss}</style>
                    </head>
                    ${templateHtml}
                </html>
                `;

                    const res = await fetch('/main/dashboard/projects/notification/create', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: JSON.stringify({
                            projectId,
                            title,
                            type,
                            expediteur: senderEmail,
                            appPassword,
                            template: finalTemplate
                        })
                    });

                    if (res.ok) {
                        showToast("Notification créée avec succès ✅");
                        closeCreateNotification();
                         setTimeout(() => {
                            location.reload();
                        }, 800);
                    } else {
                        const error = await res.json();
                        alert(error.message || "Impossible de créer la notification");
                    }

            });

        </script>