/* Import des styles de base identiques à projects.css */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #2f166b 0%, #2f166b 50%, #2f166b 100%);
    color: white;
    min-height: 100vh;
    overflow-x: hidden;
}


/* Header */
.header { 
    background: rgba(15, 23, 42, 0.8);
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    position: sticky;
    top: 0;
    z-index: 40;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.header-title {
    display: flex;
    align-items: center;
}

.project-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.project-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.project-icon i {
    font-size: 1.5rem;
    color: white;
}

.header-title h1 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-title p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.header-divider {
    width: 1px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
}

.btn-primary {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
   
}

.btn-primary:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, #3731ae 0%, #4c1e9c 100%);
}

.btn-outline {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.btn-ghost {
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    border: none;
}

.btn-ghost:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

/* Main Container */
.main-container {
    position: relative;
    z-index: 5;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    animation: fadeInUp 0.6s ease-out;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.stat-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: white;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-icon.blue { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; }
.stat-icon.green { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; }
.stat-icon.purple { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white; }
.stat-icon.yellow { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; }

/* Filters */
.filters-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 100%;
}

.search-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 3rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-size: 0.875rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.5);
}

.filters-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}
/* View Toggle */
.view-toggle {
display: flex;
    height: 2.9rem;
    width: 8rem;
    border: 3px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
}

.view-btn {
    background: transparent;
    border: none;
    border-radius: 0.2rem;
    width: 4rem;
    color: white;
    cursor: pointer;
    transition: all 0.2s;
}

.view-btn.active {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);;
}

/* Notifications Grid */
.notifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.notifications-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Notification Card */
.notification-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.notification-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-card:hover::before {
    opacity: 1;
}

.notification-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(79, 70, 229, 0.3);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.notification-info {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    flex: 1;
}

.notification-icon {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-icon.email { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
.notification-icon.sms { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
.notification-icon.push { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }

.notification-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.notification-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: white;
    margin: 0;
    line-height: 1.4;
}

.notification-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8125rem;
    color: rgba(255, 255, 255, 0.6);
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
}

.more-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.more-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}


/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    padding: 2rem;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    width: 100%;
    max-width: 900px;
    max-height: 95vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-content.large {
    max-width: 1100px;
}

.modal-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
}

.modal-header p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.modal-close {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: rgba(255, 255, 255, 0.6);
    width: 32px;
    height: 32px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.modal-body {
    padding: 2rem;
}

/* Form Styles */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-help {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.form-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 1.5rem 0;
}





.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.type-fields {
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 0.5rem;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.9);
    transition: all 0.2s ease;
    min-width: 180px;
}

.context-menu.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.context-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.context-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.context-item.danger {
    color: #ef4444;
}

.context-item.danger:hover {
    background: rgba(239, 68, 68, 0.1);
}

.context-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 0.5rem 0;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.empty-state p {
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Detail Stats */
.detail-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-stat {
    text-align: center;
}

.detail-stat-label {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.detail-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.detail-stat-value.success {
    color: #10b981;
}

/* Detail Sections */
.detail-section {
    margin-bottom: 2rem;
}

.detail-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.detail-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row span:first-child {
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
}

.detail-row span:last-child {
    color: white;
}

.detail-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Content Preview */
.content-preview {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive */
@media (max-width: 1024px) {
    .main-container {
        padding: 1.5rem;
    }
    
    .notifications-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .header-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .filters-container {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .filters-actions {
        flex-wrap: wrap;
        justify-content: space-between;
    }
    
    .notifications-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .modal {
        padding: 1rem;
    }
    
    .modal-content {
        max-height: 95vh;
    }
    
    .modal-header,
    .modal-body {
        padding: 1.5rem;
    }
    
    .detail-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .main-container {
        padding: 1rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .notification-card {
        padding: 1rem;
    }
    
    .notification-stats {
        grid-template-columns: 1fr;
    }
    
    .modal-header,
    .modal-body {
        padding: 1rem;
    }
}
.deletebtn:hover{
    background: rgba(255, 255, 255, 0.1);
    color: rgb(255, 51, 51);
}