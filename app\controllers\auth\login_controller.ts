import User from '#models/user'
import { LoginValidator } from '#validators/auth'
import type { HttpContext } from '@adonisjs/core/http'

export default class LoginController {
  async store({ request, response, auth, session }: HttpContext) {
    const data = request.only(['email', 'password'])
    const validateData = await LoginValidator.validate(data)
    const foundUser = await User.findBy('email', validateData.email)
    if (!foundUser) {
      session.flash('email_error', 'This email does not exist.')
      return response.redirect().back()
    }
    const user = await User.verifyCredentials(validateData.email, validateData.password)
    await auth.use('web').login(user)
    return response.redirect().toRoute('dashboardPage')
  }

  async google({ ally }: HttpContext) {
    return ally.use('google').redirect()
  }
  async googleCallback({ ally ,auth,response }: HttpContext) {
    const google = ally.use('google')
    /**
     * User has denied access by canceling
     * the login flow
     */
    if (google.accessDenied()) {
      return 'You have cancelled the login process'
    }

    /**
     * OAuth state verification failed. This happens when the
     * CSRF cookie gets expired.
     */
    if (google.stateMisMatch()) {
      return 'We are unable to verify the request. Please try again'
    }

    /**
     * GitHub responded with some error
     */
    if (google.hasError()) {
      return google.getError()
    }

    /**
     * Access user info
     */
    const googleUser = await google.user()
    const user = await User.firstOrCreate(
      { email: googleUser.email },
      {
        email: googleUser.email,
        password: 'google-auth-password',
        isVerified: true,
      }
    ) 
    await auth.use('web').login(user)
    return response.redirect().toRoute('dashboardPage')
  
  }
}