<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification Email - Notif Hub</title>
    <style>
        /* Reset styles pour compatibilité email */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8fafc;
            margin: 0;
            padding: 0;
        }
        
        /* Container principal */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%);
            padding: 40px 0;
        }
        
        /* Carte principale */
        .email-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            margin: 0 20px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        /* Header avec logo */
        .email-header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .email-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .logo-container {
            position: relative;
            z-index: 2;
            margin-bottom: 15px;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            animation: pulse 2s infinite;
        }
        
        .logo svg {
            width: 30px;
            height: 30px;
            fill: white;
        }
        
        .brand-name {
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 2;
        }
        
        .brand-tagline {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            margin: 5px 0 0 0;
            position: relative;
            z-index: 2;
        }
        
        /* Contenu principal */
        .email-content {
            padding: 40px 30px;
            text-align: center;
        }
        
        .welcome-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .user-email {
            color: #4f46e5;
            font-weight: 500;
        }
        
        .welcome-message {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .verification-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
            border: 1px solid #e2e8f0;
        }
        
        .verification-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
        }
        
        .verification-icon svg {
            width: 40px;
            height: 40px;
            fill: white;
        }
        
        .verification-text {
            font-size: 16px;
            color: #475569;
            margin-bottom: 25px;
        }
        
        /* Bouton de vérification */
        .verify-button {
            display: inline-block;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .verify-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .verify-button:hover::before {
            left: 100%;
        }
        
        .verify-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(79, 70, 229, 0.4);
        }
        
        /* Section d'expiration */
        .expiration-notice {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 25px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .expiration-icon {
            width: 20px;
            height: 20px;
            fill: #f59e0b;
        }
        
        .expiration-text {
            color: #92400e;
            font-size: 14px;
            font-weight: 500;
            margin: 0;
        }
        
        /* Lien alternatif */
        .alternative-link {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }
        
        .alternative-text {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 10px;
        }
        
        .copy-link {
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #475569;
            word-break: break-all;
            margin: 10px 0;
        }
        
        /* Animations */
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .email-card {
            animation: fadeIn 0.6s ease-out;
        }
        
        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                padding: 20px 0;
            }
            
            .email-card {
                margin: 0 10px;
            }
            
            .email-header {
                padding: 25px 20px;
            }
            
            .email-content {
                padding: 30px 20px;
            }
            
            .verification-section {
                padding: 25px 20px;
            }
            
            .welcome-title {
                font-size: 20px;
            }
            
            .brand-name {
                font-size: 24px;
            }
            
            .verify-button {
                padding: 14px 28px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-card">
            <!-- Header avec logo -->
            <div class="email-header">
                <div class="logo-container">
                    
                    <h1 class="brand-name">Notif Hub</h1>
                    <p class="brand-tagline">Votre plateforme de notifications</p>
                </div>
            </div>
            
            <!-- Contenu principal -->
            <div class="email-content">
                <h2 class="welcome-title">
                    Bonjour <span class="user-email">{{ user.email }}</span> ! 👋
                </h2>
                
                <p class="welcome-message">
                    Bienvenue dans la famille Notif Hub ! Nous sommes ravis de vous compter parmi nos utilisateurs. 
                    Pour commencer à utiliser votre compte, nous devons d'abord vérifier votre adresse email.
                </p>
                
                <div class="verification-section">
                    
                    
                    <p class="verification-text">
                        Cliquez sur le bouton ci-dessous pour vérifier votre adresse email et activer votre compte :
                    </p>
                    
                    <a href="{{ verificationUrl }}" class="verify-button" style="color: white;">
                        ✨ Vérifier mon email
                    </a>
                </div>
                
                <!-- Notice d'expiration -->
                <div class="expiration-notice">
                    <svg class="expiration-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,17A1,1 0 0,1 11,16A1,1 0 0,1 12,15A1,1 0 0,1 13,16A1,1 0 0,1 12,17M12,14A1,1 0 0,1 11,13V7A1,1 0 0,1 12,6A1,1 0 0,1 13,7V13A1,1 0 0,1 12,14Z"/>
                    </svg>
                    <p class="expiration-text">
                        ⏰ Ce lien expire dans 30 minutes pour votre sécurité
                    </p>
                </div>
            </div>
    </div>
</body>
</html>