import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'notifications'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('project_id').unsigned().references('id').inTable('projets').onDelete('CASCADE')
      table.string('title').notNullable()
      table.string('type').notNullable()
      table.integer('template_id').unsigned().references('id').inTable('templates').onDelete('CASCADE')
      table.string('expediteur').notNullable()
      table.string('app_password').notNullable()
      table.string('api_key').notNullable().unique()
      table.text('json_content', 'longtext').notNullable().defaultTo('{}')
      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}