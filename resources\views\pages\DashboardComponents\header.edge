<header class="header">
        <div class="header-container">
            <div class="header-left">
                <a href="{{ route('auth.logout')}}" class="btn-primary logout-btn" style="font-size:large;padding: 0.5rem 1rem;background: #ffffff00;">
                    <i class="fas fa-arrow-left"></i>
                    Log out
                </a>
                <div class="header-divider"></div>
                <div style="display: flex; flex-direction: column;margin-left:1rem ;margin-right: 1rem; align-items: center; gap: 0.2rem; margin-left: 1rem;">
                        <label style="font-size: larger;font-weight: 700;font-family:Verdana, Geneva, Tahoma, sans-serif; align:center;">email : <span style="color: rgb(182, 182, 182);font-size: large;">{{email}}</sapn></label>
                        @if(isAdmin)     
                        <span class="type-badge" style="
                            background: linear-gradient(135deg, #3bd4f6 0%, #d81d65 100%);
                            color: white;
                            padding: 0.25rem 0.75rem;
                            border-radius: 12px;
                            font-size: 0.75rem;
                            font-weight: 600;
                            width: fit-content;
                            white-space: nowrap;
                        ">admin</span>
                        @end
                </div>            
                <div class="header-divider"></div>
                    <div class="header-title">
                        <h1>Gestion des Projets</h1>
                        <p>Créez et gérez vos projets de notifications</p>                  
                    </div>
            </div>
            <div class="header-actions">
                <button class="btn-primary" onclick="document.getElementById('createProjectModal').classList.add('show');">
                    <i class="fas fa-plus"></i>
                    Nouveau Projet
                </button>
            </div>
        </div>
    </header>