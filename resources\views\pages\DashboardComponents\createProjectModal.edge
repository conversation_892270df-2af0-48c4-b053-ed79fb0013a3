<div id="createProjectModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Créer un Nouveau Projet</h2>
                <p>Configurez votre nouveau projet de notifications avec tous les détails nécessaires.</p>
                <button class="modal-close" onclick="closeCreateProject()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="createProjectForm" method="POST" action="/main/dashboard/projects">
                    {{ csrfField() }}
                    <div class="form-group">
                            <label for="name">Nom du Projet <span style="color: rgb(255, 114, 114); font-size: large;">*</span></label>
                            <input type="text" id="name" name="name" placeholder="Mon Nouveau Projet" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" placeholder="Décrivez l'objectif et l'utilisation de ce projet..." rows="3"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-plus"></i>
                            Créer le Projet
                        </button>
                        <button type="button" class="btn-outline" onclick="closeCreateProject()">
                            Annuler
                        </button>
                    </div>
                </form>
                    @if(flashMessages.get('success'))
                            <script>
                                showToast("{{ flashMessages.get('success') }}")
                            </script>
                    @endif
            </div>
        </div>
    </div>
    <script>
       function closeCreateProject() {
            document.getElementById('createProjectModal').classList.remove('show');
            document.getElementById('createProjectForm').reset();
        }
    </script>