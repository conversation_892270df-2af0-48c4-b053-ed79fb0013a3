<div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Confirmer la suprression de projet : <span id="deleteProjectName" style="color:#2563eb;font-weight:bolder;font-size: larger;"></span></h2>
                <p id="deleteProjectDescription" style="margin-bottom:0.5rem; color:rgb(205, 205, 205); font-size: medium;"></p>
                <button class="modal-close" onclick="CloseDeleteModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-actions">
                        <button type="submit" style="background-color:rgb(255, 79, 79)" class="btn-primary" id="deleteProjectBtn">
                            <i class="fas fa-trash"></i>
                            supprimer le projet
                        </button>
                        <button type="button" class="btn-outline" onclick="CloseDeleteModal()">
                            Annuler
                        </button>
                    </div>
               </form>
               @if(flashMessages.get('success'))
                            <script>
                                showToast("{{ flashMessages.get('success') }}")
                            </script>
                @endif
            </div>
        </div>
    </div>
    <script>
       window.OpenDeleteModal = OpenDeleteModal
        let currentProjectId = null;

        function CloseDeleteModal() {
            document.getElementById('deleteModal').classList.remove('show');
        }
       
        function OpenDeleteModal(project) {
                if (typeof project === 'string') {
                    project = JSON.parse(project)
                }
                currentProjectId = project.id;
                // Populate modal fields
                const nameElem = document.getElementById('deleteProjectName')
                const descElem = document.getElementById('deleteProjectDescription')
                if (nameElem)
                    nameElem.textContent = project.name
                if (descElem) 
                    descElem.textContent = project.description  
                const modal = document.getElementById('deleteModal')
                if (modal) {
                    modal.classList.add('show')
                }
        } 
        
        document.getElementById('deleteProjectBtn').addEventListener('click', () => {
            if (!currentProjectId) return;
            fetch(`/main/dashboard/projects/${currentProjectId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(res => {
                if (res.ok) {
                    CloseDeleteModal();
                    location.reload();
                } else {
                    alert('Erreur lors de la suppression du projet');
                }
            })
            .catch(err => console.error(err));
        });
    </script>