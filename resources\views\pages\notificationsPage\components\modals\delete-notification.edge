<div id="deleteModal" class="modal">
        <div class="modal-content" style="width: 40rem;">
            <div class="modal-header">
                <h2>Confirmer la suprression de notification :</h2>
                <button class="modal-close" onclick="CloseDeleteModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <h1 id="deleteNotificationName" align="center" style="color:#eb2528;font-weight:bolder;font-size:xx-large;"></h1>
                <form>
                    <div class="form-actions">
                        <button type="submit" style="background-color:rgb(255, 79, 79)" class="btn-primary" id="deleteNotificationBtn">
                            <i class="fas fa-trash"></i>
                            supprimer la notification
                        </button>
                        <button type="button" class="btn-outline" style="width: 9rem;" onclick="CloseDeleteModal()">
                            Annuler
                        </button>
                    </div>
               </form>

            </div>
        </div>
    </div>
    <script>
       window.OpenDeleteModal = OpenDeleteModal
        let currentProjectId = null;

        function CloseDeleteModal() {
            document.getElementById('deleteModal').classList.remove('show');
        }
       
        function OpenDeleteModal(notification) {
                if (typeof notification === 'string') {
                    notification = JSON.parse(notification)
                }
                currentNotificationId = notification.id;
                // Populate modal fields
                const nameElem = document.getElementById('deleteNotificationName')
                if (nameElem)
                    nameElem.textContent = notification.title
 
                const modal = document.getElementById('deleteModal')
                if (modal) {
                    modal.classList.add('show')
                }
        } 
        
        document.getElementById('deleteNotificationBtn').addEventListener('click', (e) => {
            e.preventDefault();
            if (!currentNotificationId) return;
            fetch(`/main/dashboard/projects/notification/${currentNotificationId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(res => {
                if (res.ok) {
                    showToast("Notification supprimée avec succès ✅");
                    CloseDeleteModal();
                    setTimeout(() => {
                        location.reload();
                    }, 800);
                } else {
                    alert('Erreur lors de la suppression du projet');
                }
            })
            .catch(err => console.error(err));
        });
    </script>