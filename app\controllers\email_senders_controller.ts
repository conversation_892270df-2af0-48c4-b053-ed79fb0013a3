// app/Controllers/Http/EmailSendersController.ts
import type { HttpContext } from '@adonisjs/core/http'
import Notification from '#models/notification'
import Template from '#models/template'
import nodemailer from 'nodemailer'

export default class EmailSendersController {
  public async SendEmail({ request, response }: HttpContext) {
    try {
      const { apiKey, destinateur, ...variables } = request.body()

      const notification = await Notification.query().where('api_key', apiKey).firstOrFail()

      const template = await Template.findOrFail(notification.templateId)
      let html = template.html

      for (const [key, value] of Object.entries(variables)) {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
        html = html.replace(regex, String(value))
      }

     
      const transporter = nodemailer.createTransport({
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        auth: {
          user: notification.expediteur,
          pass: notification.appPassword,
        },
      })

      await transporter.sendMail({
        from: notification.expediteur,
        to: destinateur,
        subject: notification.title,
        html: html,
      })

      return response.ok({ message: 'Email sent successfully' })
    } catch (error) {
      console.error(error)
      return response.badRequest({
        message: 'Error while sending email',
        error: error.message,
      })
    }
  }
}
