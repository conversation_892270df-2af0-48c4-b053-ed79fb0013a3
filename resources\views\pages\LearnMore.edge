<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>
      Learn More - <PERSON>if <PERSON>
    </title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #1e293b 100%);
        color: white;
        min-height: 100vh;
        line-height: 1.6;
      }
      
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 30px 24px;
      }
      
      .text-center {
        text-align: center;
      }
      
      .mb-12 {
        margin-bottom: 48px;
      }
      
      .mt-4 {
        margin-top: 16px;
      }
      
      .mt-3 {
        margin-top: 12px;
      }
      
      .mt-10 {
        margin-top: 40px;
      }
      
      .text-4xl {
        font-size: 36px;
      }
      
      .text-2xl {
        font-size: 24px;
      }
      
      .text-lg {
        font-size: 18px;
      }
      
      .text-base {
        font-size: 16px;
      }
      
      .font-extrabold {
        font-weight: 800;
      }
      
      .font-semibold {
        font-weight: 600;
      }
      
      .font-medium {
        font-weight: 500;
      }
      
      .text-gray-900 {
        color: #ffffff;
      }
      
      .text-gray-600 {
        color: #d1d5db;
      }
      
      .text-gray-700 {
        color: #e5e7eb;
      }
      
      .text-indigo-600 {
        color: #60a5fa;
      }
      
      .leading-relaxed {
        line-height: 1.625;
      }
      
      .grid {
        display: grid;
      }
      
      .gap-10 {
        gap: 40px;
      }
      
      .md\:grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .space-y-3 > * + * {
        margin-top: 12px;
      }
      
      .list-disc {
        list-style-type: disc;
      }
      
      .list-inside {
        list-style-position: inside;
      }
      
      .inline-block {
        display: inline-block;
      }
      
      .px-8 {
        padding-left: 32px;
        padding-right: 32px;
      }
      
      .py-3 {
        padding-top: 12px;
        padding-bottom: 12px;
      }
      
      .text-white {
        color: white;
      }
      
      .bg-indigo-600 {
        background-color: #2563eb;
      }
      
      .rounded-md {
        border-radius: 8px;
      }
      
      .hover\:bg-indigo-700:hover {
        background-color: #1d4ed8;
      }
      
      /* Custom animations and enhancements */
      
      .container {
        animation: fadeInUp 1s ease-out;
      }
      
      .grid > div {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        padding: 32px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      
      .grid > div:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      }
      
      .grid > div h3 {
        position: relative;
        padding-bottom: 12px;
      }
      
      .grid > div h3::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(90deg, #60a5fa, #a78bfa);
        border-radius: 2px;
      }
      
      ul li {
        position: relative;
        padding-left: 8px;
        transition: color 0.3s ease;
      }
      
      ul li:hover {
        color: #93c5fd;
      }
      
      code {
        background: rgba(96, 165, 250, 0.2);
        color: #93c5fd;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
      }
      
      .cta-button {
        background: linear-gradient(135deg, #2563eb, #1d4ed8);
        border: none;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        position: relative;
        overflow: hidden;
      }
      
      .cta-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }
      
      .cta-button:hover::before {
        left: 100%;
      }
      
      .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
      }
      
      /* Header with back button */
      
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 0;
      }
      
      .back-button {
        display: flex;
        align-items: center;
        gap: 8px;
        background: transparent;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        text-decoration: none;
        transition: all 0.3s ease;
      }
      
      .back-button:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(-2px);
      }
      
      .logo-section {
        display: flex;
        align-items: center;
        gap: 12px;
      }
      
      .logo-icon {
        width: 40px;
        height: 40px;
        background: #2563eb;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }
      
      /* Responsive design */
      
      @media (max-width: 768px) {
        .md\:grid-cols-2 {
          grid-template-columns: 1fr;
        }
      
        .text-4xl {
          font-size: 28px;
        }
      
        .container {
          padding: 40px 16px;
        }
      
        .grid > div {
          padding: 24px;
        }
      
        .header {
          flex-direction: column;
          gap: 20px;
          text-align: center;
        }
      }
      
      /* Animation keyframes */
      
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
      
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      /* Feature highlights */
      
      .feature-highlight {
        background: linear-gradient(135deg, rgba(96, 165, 250, 0.1), rgba(167, 139, 250, 0.1));
        border-left: 4px solid #60a5fa;
        padding: 20px;
        margin: 20px 0;
        border-radius: 8px;
      }
      
      .feature-highlight h4 {
        color: #93c5fd;
        font-weight: 600;
        margin-bottom: 8px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- Header with back button and logo -->
      <div class="header">
        <a href="{{ route('home') }}" class="back-button">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M19 12H5M12 19l-7-7 7-7" />
          </svg>
          Back to Home
        </a>
        <div class="logo-section">
          <div class="logo-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
            </svg>
          </div>
          <span class="font-semibold">Notif Hub</span>
        </div>
      </div>

      <div class="text-center mb-12">
        <h2 class="text-4xl font-extrabold text-gray-900">
          About Notif Hub
        </h2>
        <p class="mt-4 text-lg text-gray-600">
          A powerful centralized notification system designed for developers and tech teams.
        </p>
      </div>

      <div class="grid gap-10 md:grid-cols-2">
        <div>
          <h3 class="text-2xl font-semibold text-indigo-600">
            What is Notif Hub?
          </h3>
          <p class="mt-4 text-gray-700 text-base leading-relaxed">
            Notif Hub is a SaaS web application that allows users to create projects and manage notifications (Email & SMS) through a simple and customizable API.
                    Every user can create multiple projects, and each project contains its own set of notifications. The platform supports providers like Twilio, SendGrid, and Mailgun.
          </p>
          <p class="mt-3 text-gray-700 text-base leading-relaxed">
            Whether you are building a web app, mobile app, or internal tool, Notif Hub helps you reach your users instantly using your preferred service provider.
          </p>

          <div class="feature-highlight">
            <h4>
              🚀 Key Benefits
            </h4>
            <p>
              Streamline your notification workflow with our unified API that works across multiple providers and platforms.
            </p>
          </div>
        </div>

        <div>
          <h3 class="text-2xl font-semibold text-indigo-600">
            How Does It Work?
          </h3>
          <ul class="mt-4 space-y-3 text-gray-700 text-base list-disc list-inside">
            <li>
              Sign up and create an account
            </li>
            <li>
              Create one or more projects to group your notifications
            </li>
            <li>
              Select the type of notifications you want to enable: Email, SMS, or both
            </li>
            <li>
              Choose and configure your service provider (e.g. Twilio, SendGrid)
            </li>
            <li>
              Create custom templates using dynamic variables like <code>{{ username }}</code>
            </li>
            <li>
              Use the auto-generated API key to send notifications from your app
            </li>
            <li>
              Access Swagger documentation to test and explore the API endpoints
            </li>
          </ul>

          <div class="feature-highlight">
            <h4>
              📊 Advanced Features
            </h4>
            <p>
              Monitor delivery rates, track engagement, and get detailed analytics for all your notification campaigns.
            </p>
          </div>
        </div>
      </div>

      <div class="mt-10 text-center">
        <a
          href="{{ route('registerPage') }}"
          class="inline-block px-8 py-3 text-white bg-indigo-600 rounded-md hover:bg-indigo-700 text-lg font-medium cta-button"
        >
          Start Using Notif Hub
            </a>
      </div>
    </div>
  </body>
</html>
