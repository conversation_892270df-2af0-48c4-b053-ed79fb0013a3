// Global variables
let notifications = window.notifications || []


let currentProject = {
  id: '1',
  name: 'E-commerce Platform',
  description:
    'Notifications pour la boutique en ligne avec confirmations de commandes, codes promo et alertes de stock',
  type: 'both',
  icon: 'fas fa-shopping-cart',
}

let currentFilters = {
  search: '',
  status: 'all',
  type: 'all',
  template: 'all',
}

let selectedNotificationId = null

// Initialize the page
document.addEventListener('DOMContentLoaded', function () {
  renderNotifications()
  setupEventListeners()
})

// Setup event listeners
function setupEventListeners() {
  // Search input
  const searchInput = document.getElementById('searchInput')
  searchInput.addEventListener('input', function (e) {
    currentFilters.search = e.target.value
    renderNotifications()
  })

  // Close modals when clicking outside
  document.addEventListener('click', function (e) {
    if (e.target.classList.contains('modal')) {
      closeAllModals()
    }
  })

  // Context menu
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault()
  })


}
// Filter notifications
function filterNotifications() {
  return notifications.filter((notification) => {
    const matchesSearch =
      notification.title.toLowerCase().includes(currentFilters.search.toLowerCase())
    const matchesType = currentFilters.type === 'all' || notification.type === currentFilters.type
  

    return matchesSearch && matchesType 
  })
}
// Render notifications
function renderNotifications() {
  const filteredNotifications = filterNotifications()
  const container = document.getElementById('notificationsContainer')
  const emptyState = document.getElementById('emptyState')

  if (filteredNotifications.length === 0) {
    container.style.display = 'none'
    emptyState.style.display = 'block'
    return
  }

  // Always grid view
  container.style.display = 'grid'
  container.className = 'notifications-grid'
  emptyState.style.display = 'none'

  container.innerHTML = filteredNotifications
    .map((notification) => createNotificationCard(notification))
    .join('')
}


 // Create notification card (grid view)
function createNotificationCard(notification) {
  const typeIcon = getTypeIcon(notification.type)

  return `
       <div class="notification-card fade-in" onclick="openNotificationDetails('${notification.id}')">
            <div class="notification-header">
                <div class="notification-info">
                    <div class="notification-icon ${notification.type}">
                        ${typeIcon}
                    </div>
                   <div style="display: flex; flex-direction: column; gap: 0rem; min-width: 0;">
                      <div class="notification-title">
                          <h3 class="notification-name" style="margin: 0;">
                              ${notification.title}
                          </h3>
                      </div>
                      <span class="type-badge" style="
                          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                          color: white;
                          padding: 0.25rem 0.75rem;
                          border-radius: 12px;
                          font-size: 0.75rem;
                          font-weight: 500;
                          width: fit-content;
                          white-space: nowrap;
                      ">
                          ${notification.type}
                      </span>
                  </div>


                </div>
                <div class="project-actions" style="display: flex;gap: 0.3rem;">
                    <button class="more-btn" title="Modider la notification" onclick='event.stopPropagation();openEditNotification(${JSON.stringify(notification).replace(/'/g, '&apos;')})'>
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="more-btn deletebtn" title="Supprimer la notification" onclick='event.stopPropagation();OpenDeleteModal(${JSON.stringify(notification).replace(/'/g, '&apos;')})'>
                        <i class="fas fa-trash"></i>
                    </button>
                </div>   
            </div>
                        <div>
                            <label style="display: block; margin:1px;font-size: 0.875rem; font-weight: 500; margin-bottom: 0.5rem; color: rgba(255,255,255,0.8);">Email expéditeur:</label>
                            <label style="display: block;margin:2px;width: 100%; padding: 0.5rem; border: 1px solid rgba(255,255,255,0.2); border-radius: 6px; background: rgba(255,255,255,0.05); color: white; font-size: 0.875rem;" >
                                ${notification.expediteur}
                            </label> 
                         </div>
                                                 <div>
                            <label style="display: block;margin-top:10px; font-size: 0.875rem; font-weight: 500; margin-bottom: 0.5rem; color: rgba(255,255,255,0.8);">URL de l'API:</label>
                            <label style="display: block;margin:2px;width: 100%; padding: 0.5rem; border: 1px solid rgba(255,255,255,0.2); border-radius: 6px; background: rgba(255,255,255,0.05); color: white; font-size: 0.875rem;" >
                                http://localhost:3333/Notif-Hub
                            </label>
                            
                        </div>
            <div class="notification-swagger">
                <div class="swagger-header" style="display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                    <h4 style="margin: 0;">📑 Configuration API</h4>
                </div>
                <div class="swagger-content" style="padding: 1rem 0;">
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-size: 0.875rem; font-weight: 500; margin-bottom: 0.5rem; color: rgba(255,255,255,0.8);">Clé API:</label>
                        <label style="display: block;margin:2px;width: 100%; padding: 0.5rem; border: 1px solid rgba(255,255,255,0.2); border-radius: 6px; background: rgba(255,255,255,0.05); color: white; font-size: 0.875rem;" >
                                  ${notification.apiKey}
                            </label>
                    </div>
                    <h5 style="margin: 0 0 0.5rem 0; font-size: 0.875rem; color: rgba(255,255,255,0.8);">Données attendues:</h5>
                    <pre class="swagger-box" style="background: rgba(0,0,0,0.3); padding: 1rem; border-radius: 6px; font-size: 0.75rem; overflow-x: auto;">
${JSON.stringify(JSON.parse(notification.jsonContent), null, 2)}                
                    </pre>
                </div>
            </div>
            <div class="notification-footer">
                <span>Créée : ${new Date(notification.createdAt).toLocaleDateString()}</span>
                <span>dernier mise a jour : ${new Date(notification.updatedAt).toLocaleDateString()}</span>
            </div>
        </div>
    `
}

// Get type icon
function getTypeIcon(type) {
  switch (type) {
    case 'email':
      return '<i class="fas fa-envelope"></i>'
    case 'sms':
      return '<i class="fas fa-sms"></i>'
    case 'push':
      return '<i class="fas fa-mobile-alt"></i>'
    default:
      return '<i class="fas fa-bell"></i>'
  }
}

// Set type filter
function setTypeFilter(type) {
  currentFilters.type = type
  const typeText = type === 'all' ? 'Tous' : type.charAt(0).toUpperCase() + type.slice(1)
  document.getElementById('typeFilterText').textContent = `Type: ${typeText}`
  closeAllDropdowns()
  renderNotifications()
}


// Close all modals
function closeAllModals() {
  const modals = document.querySelectorAll('.modal')
  modals.forEach((modal) => modal.classList.remove('show'))
}

// Show toast notification
function showToast(message) {
  const toast = document.createElement('div')
  toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        font-weight: 500;
        z-index: 1000;
        animation: slideInRight 0.3s ease-out;
        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
    `
  toast.textContent = message

  // Add animation keyframes if not already added
  if (!document.getElementById('toast-styles')) {
    const style = document.createElement('style')
    style.id = 'toast-styles'
    style.textContent = `
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
            @keyframes slideOutRight {
                from {
                    opacity: 1;
                    transform: translateX(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }
        `
    document.head.appendChild(style)
  }

  document.body.appendChild(toast)

  setTimeout(() => {
    toast.style.animation = 'slideOutRight 0.3s ease-out'
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast)
      }
    }, 300)
  }, 4000)
}
window.showToast = showToast
// Keyboard shortcuts
document.addEventListener('keydown', function (e) {
  // Escape key to close modals
  if (e.key === 'Escape') {
    closeAllModals()
    closeAllDropdowns()
    document.getElementById('contextMenu').classList.remove('show')
  }
})
