{"name": "Notif", "version": "0.0.0", "private": true, "type": "module", "license": "UNLICENSED", "scripts": {"start": "node bin/server.js", "build": "node ace build", "dev": "node ace serve --hmr", "test": "node ace test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "imports": {"#controllers/*": "./app/controllers/*.js", "#exceptions/*": "./app/exceptions/*.js", "#models/*": "./app/models/*.js", "#mails/*": "./app/mails/*.js", "#services/*": "./app/services/*.js", "#listeners/*": "./app/listeners/*.js", "#events/*": "./app/events/*.js", "#middleware/*": "./app/middleware/*.js", "#validators/*": "./app/validators/*.js", "#providers/*": "./providers/*.js", "#policies/*": "./app/policies/*.js", "#abilities/*": "./app/abilities/*.js", "#database/*": "./database/*.js", "#tests/*": "./tests/*.js", "#start/*": "./start/*.js", "#config/*": "./config/*.js"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/eslint-config": "^2.0.0", "@adonisjs/prettier-config": "^1.4.4", "@adonisjs/tsconfig": "^1.4.0", "@japa/assert": "^4.0.1", "@japa/plugin-adonisjs": "^4.0.0", "@japa/runner": "^4.2.0", "@swc/core": "1.11.24", "@types/cuid": "^1.3.1", "@types/jsonwebtoken": "^9.0.10", "@types/luxon": "^3.6.2", "@types/node": "^22.15.18", "autoprefixer": "^10.4.21", "eslint": "^9.26.0", "hot-hook": "^0.4.0", "pino-pretty": "^13.0.0", "postcss": "^8.5.6", "prettier": "^3.5.3", "tailwindcss": "^3.4.3", "ts-node-maintained": "^10.9.5", "typescript": "~5.8", "vite": "^6.3.5"}, "dependencies": {"@adonisjs/ally": "^5.1.0", "@adonisjs/auth": "^9.4.0", "@adonisjs/core": "^6.19.0", "@adonisjs/lucid": "^21.6.1", "@adonisjs/mail": "^9.2.2", "@adonisjs/session": "^7.5.1", "@adonisjs/shield": "^8.2.0", "@adonisjs/static": "^1.1.1", "@adonisjs/vite": "^4.0.0", "@vinejs/vine": "^3.0.1", "better-sqlite3": "^12.2.0", "cuid": "^3.0.0", "edge.js": "^6.2.1", "grapesjs": "^0.22.12", "jsonwebtoken": "^9.0.2", "luxon": "^3.7.1", "node-cron": "^4.2.1", "reflect-metadata": "^0.2.2", "sqlite3": "^5.1.7"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts"]}, "prettier": "@adonisjs/prettier-config"}