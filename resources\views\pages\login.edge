<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <title>title</title>
    @vite('resources/js/app.js')
    @vite('resources/css/HomePage.css')
</head>
<body>
    <div id="login-view" class="view auth-view">
        <div class="auth-container">
            <div class="auth-card animate-scale-in">
                <div class="auth-header">
                    <div class="auth-nav">
                        <a href="{{ route('home') }}" class="btn-back">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M19 12H5M12 19l-7-7 7-7"/>
                            </svg>
                            Back
                        </a>
                        <div class="auth-logo">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                            </svg>
                        </div>
                    </div>
                    <h2 class="auth-title">Welcome Back</h2>
                    <p class="auth-description">Sign in to your Notif Hub account</p>
                </div>
                <div class="auth-content">
                    <form class="auth-form" action="{{ route('auth.login') }}" method="POST">
                        {{ csrfField()  }}
                        <div class="form-group">
                            <label for="login-email">Email</label>
                            <input type="email" id="email" name="email" placeholder="Enter your email" required>
                            @inputError('email')
                                <p class="text-red-500 text-sm mt-2">{{ $messages.join(', ') }}</p>
                            @end
                            @if(flashMessages.get('email_error'))
                                <p class="text-red-500 text-sm mt-2">
                                    {{ flashMessages.get('email_error') }}
                                </p>
                            @endif
                        </div>
                        <div class="form-group">
                            <label for="login-password">Password</label>
                            <input type="password" id="password" name="password" placeholder="Enter your password" required>
                            @inputError('password')
                                <p class="text-red-500 text-sm mt-2">{{ $messages.join(', ') }}</p>
                            @end
                            @error('E_INVALID_CREDENTIALS')
                                <p class="text-red-500 text-sm mt-2">incorrect password</p>
                            @end      
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">Sign In</button>
                        <p> --------------------------- <span>OR</span> ----------------------------</p>
                        <a href="{{route('auth.googleLogin')}}" class="flex items-center justify-center gap-2 bg-white text-gray-900 border-none rounded-lg shadow-md px-6 py-2 text-sm font-medium hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors cursor-pointer">
                            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="-0.5 0 48 48" fill="none">
                                <g>
                                    <path d="M9.83,24c0-1.52.25-2.98.7-4.36L2.62,13.6C1.08,16.73.21,20.26.21,24c0,3.74.87,7.26,2.41,10.39l7.9-6.05c-.45-1.36-.7-2.82-.7-4.34Z" fill="#FBBC05"/>
                                    <path d="M23.71,10.13c3.31,0,6.3,1.17,8.65,3.1l6.84-6.83C35.04,2.77,29.7.53,23.71.53c-9.29,0-17.27,5.31-21.09,13.07l7.91,6.04c1.82-5.53,6.98-9.51,13.18-9.51Z" fill="#EB4335"/>
                                    <path d="M23.71,37.87c-6.16,0-11.41-3.98-13.18-9.51l-7.91,6.04c3.82,7.76,11.8,13.08,21.09,13.08,5.73,0,11.2-2.04,15.31-5.85l-7.51-5.8c-2.12,1.33-4.79,2.05-7.8,2.05Z" fill="#34A853"/>
                                    <path d="M46.15,24c0-1.39-.21-2.88-.53-4.27H23.71v9.07h12.6c-.63,3.09-2.34,5.47-4.79,7.01l7.51,5.8C43.34,37.61,46.15,31.65,46.15,24Z" fill="#4285F4"/>
                                </g>
                            </svg>
                            <span>Continue with Google</span>
                        </a>
             
                    </form>
                    <div class="auth-switch">
                        <a href="{{ route('registerPage') }}" class="link-button">
                            Don't have an account? Register here
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>