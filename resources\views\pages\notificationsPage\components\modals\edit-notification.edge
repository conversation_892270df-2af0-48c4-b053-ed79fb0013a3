<div id="editNotificationModal" class="modal">
    <div class="modal-content large">
        <div class="modal-header">
            <h2>Modifier la Notification</h2>
            <p>Met<PERSON>z à jour les détails de votre notification.</p>
            <button class="modal-close" onclick="closeEditNotification()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="editNotificationForm">
                {{ csrfField() }}
                <div class="form-row">
                    <div class="form-group">
                        <label for="editNotificationTitle">Titre de la Notification *</label>
                        <input type="text" id="editNotificationTitle" required>
                    </div>
                    <div class="form-group">
                        <label for="editNotificationType">Type de Notification *</label>
                        <select id="editNotificationType" required onchange="updateTypeFields()">
                            <option value="email" style="color: black;">Email</option>
                            <option value="sms" style="color: black;">SMS</option>
                            <option value="push" style="color: black;">Push Notification</option>
                        </select>
                    </div>
                </div>
                <label for="editNotificationTemplate">Template *</label>
                <div id="gjs-edit"></div>
                <br><br>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editNotificationEmail">Email expéditeur *</label>
                        <input type="email" id="editNotificationEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="editNotificationPassword">Mot de passe d’application *</label>
                        <input type="password" id="editNotificationPassword" required>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-primary" style="width:12rem;">
                        <i class="fas fa-save"></i>
                        Mettre à jour
                    </button>
                    <button type="button" class="btn-outline" style="width:8rem;border-radius: 0.5rem;" onclick="closeEditNotification()">
                        Annuler
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    const editorEdit = grapesjs.init({
        container: '#gjs-edit',
        height: '100vh',
        fromElement: false,
        storageManager: false,
        plugins: ['gjs-preset-newsletter', 'gjs-blocks-basic'],
        pluginsOpts: {
            'gjs-preset-newsletter': {
                modalTitleImport: 'Import template',
                modalTitleExport: 'Export template',
                codeViewerTheme: 'material',
            }
        }
    });
    editorEdit.BlockManager.add('merge-var', {
        label: 'Variable',
        category: 'Basic',
        attributes: { class: 'fa fa-code', title: 'Variable Block' },
        content:'<span>&#123;&#123; variableName &#125;&#125;</span>'
    });
    window.openEditNotification = openEditNotification;
    let currentNotificationId = null;
    function parseTemplate(finalTemplate) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(finalTemplate, 'text/html');

        // Récupérer le CSS
        let css = '';
        const styleTag = doc.querySelector('style');
        if (styleTag) {
            css = styleTag.innerHTML;
        }

        // Récupérer le HTML à l'intérieur de <body>
        let html = '';
        if (doc.body) {
            html = doc.body.innerHTML;
        }

        return { css, html };
    }
    async function openEditNotification(notification) {
        document.getElementById('editNotificationModal').classList.add('show');
        currentNotificationId = notification.id;

        // Pré-remplir les champs
        document.getElementById('editNotificationTitle').value = notification.title;
        document.getElementById('editNotificationType').value = notification.type;
        document.getElementById('editNotificationEmail').value = notification.expediteur;
        document.getElementById('editNotificationPassword').value = notification.appPassword;


        const res = await fetch(`/main/dashboard/templates/${notification.templateId}`);
            if (!res.ok) throw new Error("Impossible de charger le template");
            const data = await res.json();
            const template = data.template;

            const { css, html } = parseTemplate(template.html);
            editorEdit.setComponents(html || '');
            editorEdit.setStyle(css || '');
            setTimeout(() => editorEdit.refresh(), 100);

    }
    function closeEditNotification() {
        document.getElementById('editNotificationModal').classList.remove('show');
        currentNotificationId = null;
    }
    document.getElementById('editNotificationForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        const title = document.getElementById('editNotificationTitle').value;
        const type = document.getElementById('editNotificationType').value;
        const senderEmail = document.getElementById('editNotificationEmail').value;
        const appPassword = document.getElementById('editNotificationPassword').value;

        const templateHtml = editorEdit.getHtml();
        const templateCss  = editorEdit.getCss();
        const finalTemplate = `
        <html>
            <head>
            <style>${templateCss}</style>
            </head>
            ${templateHtml}
        </html>
        `;

        const res = await fetch(`/main/dashboard/projects/notification/${currentNotificationId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                title,
                type,
                expediteur: senderEmail,
                appPassword,
                template: finalTemplate
            })
        });

        if (res.ok) {
            showToast("Notification mise à jour avec succès ✅");
            closeEditNotification();
            setTimeout(() => {
                location.reload();
            }, 800);
        } else {
            const error = await res.json();
            alert(error.message || "Impossible de mettre à jour la notification");
        }
    });
</script>
