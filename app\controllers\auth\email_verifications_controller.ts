// app/controllers/auth/email_verification_controller.ts
import type { HttpContext } from '@adonisjs/core/http'
import User from '#models/user'

export default class EmailVerificationController {
  async verify({ request, response, auth }: HttpContext) {
    const email = request.input('email')
    const token = request.input('token')
    if (!email || !token) {
      return response.badRequest('Email ou token manquant.')
    }
    const user = await User.findBy('email', email)

    if (!user || user.verificationToken !== token) {
      return response.badRequest('Lien invalide ou expiré.')
    }

    user.isVerified = true
    user.verificationToken = null
    await user.save()

    await auth.use('web').login(user)
    return response.redirect().toRoute('dashboardPage')
  }
}
