import { DateTime } from 'luxon'
import { BaseModel, column, beforeCreate } from '@adonisjs/lucid/orm'
import { randomBytes } from 'node:crypto'

export default class Notification extends BaseModel {
  @column({ isPrimary: true })
  declare id: number
  @column()
  declare projectId: number
  @column()
  declare title: string
  @column()
  declare type: string
  @column()
  declare templateId: number
  @column()
  declare expediteur: string
  @column()
  declare appPassword: string
  @column()
  declare apiKey: string
  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime
  @column()
  declare jsonContent: string
  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
  @beforeCreate()
  static generateApiKey(notification: Notification) {
      notification.apiKey = randomBytes(12).toString('base64url')     
  }
}