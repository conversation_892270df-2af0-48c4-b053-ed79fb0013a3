/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import LoginController from '#controllers/auth/login_controller'
import RegisterController from '#controllers/auth/register_controller'
import ProjectsController from '#controllers/projects_controller'
import NotificationsController from '#controllers/notifications_controller'
import router from '@adonisjs/core/services/router'
import { middleware } from './kernel.js'
import EmailVerificationController from '#controllers/auth/email_verifications_controller'
import EmailSendersController from '#controllers/email_senders_controller'

//pages
router.on('/').render('pages/home').as('home')
router.on('/auth/register').render('pages/register').as('registerPage')
router.on('/auth/login').render('pages/login').as('loginPage')
router.on('/auth/LearnMore').render('pages/LearnMore').as('LearnMorePage')
router.resource

router.group(() => {
  router.post('/register',[RegisterController, 'store']).as('register')
  router.post('/login', [LoginController, 'store']).as('login')
  router.get('/logout', async ({ auth, response }) => {
    await auth.use('web').logout()
    return response.redirect().toRoute('home')
  }).as('logout')
  router.get('/google',[LoginController, 'google']).as('googleLogin')
  router.get('/google/callback', [LoginController, 'googleCallback']).as('googleCallback')  
  router.get('/verifyEmail', [EmailVerificationController, 'verify']).as('verifyEmail')
  
    
}).prefix('/auth').as('auth')


router.group(() => {
    router.on('/main/dashboard').render('pages/dashboard').as('dashboardPage')
    router.resource('/main/dashboard/projects', ProjectsController).only(['index', 'store', 'update', 'destroy'])
    router.get('/main/dashboard/projects/:id/notifications', [ProjectsController, 'notificationsPage']).as('notificationsPage')
    router.post('/main/dashboard/projects/notification/create', [NotificationsController,'store']).as('createNotification')
    router.delete('/main/dashboard/projects/notification/:id', [NotificationsController,'destroy']).as('deleteNotification')
    router.put('/main/dashboard/projects/notification/:id', [NotificationsController,'update']).as('updateNotification')
    router.get('/main/dashboard/templates/:id', [NotificationsController,'getTemplate']).as('getTemplate')
  }).use([middleware.auth(), middleware.ensureEmailVerified() ])


router.post('/Notif-Hub', [EmailSendersController, 'SendEmail']).as('emailSender')