* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-x: hidden;
}

/* View Management */
.view {
  display: none;
  min-height: 100vh;
}

.view.active {
  display: block;
}

/* Landing Page Styles */
#landing-view {
  background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #1e293b 100%);
  color: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 0;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 2%;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: #2563eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.logo-text h1 {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.logo-text p {
  font-size: 14px;
  color: #d1d5db;
}

.auth-buttons {
  display: flex;
  gap: 1.3rem;
  margin-right: 2%;
}

/* Main Content */
.main-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 48px 0;
  gap: 38px;
  padding-left: 7%;
  padding-top: 70px;
}

.content-left {
  flex: 1;
}

.hero-title {
  font-size: 60px;
  font-weight: bold;
  line-height: 1.1;
  margin-bottom: 24px;
}

.text-blue {
  color: #60a5fa;
}

.text-purple {
  color: #a78bfa;
}

.hero-description {
  font-size: 20px;
  color: #d1d5db;
  line-height: 1.6;
  margin-bottom: 32px;
  max-width: 650px;
}

.hero-buttons {
  display: flex;
  gap: 16px;
  margin-top:7%;
}

.content-right {
  flex: 1;
  display: flex;
  justify-content: center;
}

.illustration-container {
  position: relative;
}

.illustration-card {
  width: 470px;
  height: 306px;
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.2) 0%, rgba(167, 139, 250, 0.2) 100%);
  border-radius: 16px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-container {
  display: flex;
  align-items: center;
  gap: 70px;
}

.icon {
  color: #60a5fa;
}

.icon:last-child {
  color: #a78bfa;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn:hover {
  transform: scale(1.05);
}

.btn-primary {
  background: #2563eb;
  color: white;
}

.btn-primary:hover {
  background: #1d4ed8;
}

.btn-outline {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

.btn-full {
  width: 100%;
  justify-content: center;
}

.arrow-icon {
  transition: transform 0.3s ease;
}

.btn:hover .arrow-icon {
  transform: translateX(4px);
}

/* Auth Views */
.auth-view {
  background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #1e293b 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.auth-container {
  width: 100%;
  max-width: 450px;
}

.auth-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 32px;
  color: white;
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.btn-back {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.btn-back:hover {
  background: rgba(255, 255, 255, 0.1);
}

.auth-logo {
  width: 40px;
  height: 40px;
  background: #2563eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.auth-description {
  color: #d1d5db;
  font-size: 14px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
}

.form-group input {
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
}

.form-group input::placeholder {
  color: #9ca3af;
}

.form-group input:focus {
  outline: none;
  border-color: #60a5fa;
}

.auth-switch {
  text-align: center;
  margin-top: 24px;
}

.link-button {
  background: none;
  border: none;
  color: #60a5fa;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.3s ease;
}

.link-button:hover {
  color: #93c5fd;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounceSlow {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 1s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 1s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 1s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-bounce-slow {
  animation: bounceSlow 2s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.delay-300 {
  animation-delay: 0.3s;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    text-align: center;
  }

  .hero-title {
    font-size: 36px;
  }

  .illustration-card {
    width: 300px;
    height: 200px;
  }

  .header {
    flex-direction: column;
    gap: 20px;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .icon-container {
    gap: 20px;
  }

  .icon svg {
    width: 48px;
    height: 48px;
  }
}

@media (max-width: 480px) {
  .auth-card {
    padding: 24px;
  }

  .hero-title {
    font-size: 28px;
  }

  .hero-description {
    font-size: 16px;
  }
}
