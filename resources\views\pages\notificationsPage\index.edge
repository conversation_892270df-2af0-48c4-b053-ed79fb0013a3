<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrfToken }}">
    <title>Notifications - Notif Hub</title>
    @vite('resources/css/notifications.css')
    @vite('resources/js/notifications.js')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        window.notifications = {{{ JSON.stringify(notifications) }}};
        window.currentProject = {{{ JSON.stringify(project) }}};
    </script>

</head>
<body>
    <!-- header -->
    @component('./pages/notificationsPage/components/header', { project })      
    @end
    <div class="main-container">
        <!-- Filters and Search -->
        @component('./pages/notificationsPage/components/filters', {  })      
        @end
        <!-- Notifications Display -->
         @component('./pages/notificationsPage/components/dispalyNotifications.edge', {  })      
         @end

    </div>

    <!-- Create Notification Modal -->
     @component('./pages/notificationsPage/components/modals/create-notification', { project })      
     @end
    <!-- delete Notification Modal -->
     @component('./pages/notificationsPage/components/modals/delete-notification', {  })      
     @end
    <!-- Edit Notification Modal -->
     @component('./pages/notificationsPage/components/modals/edit-notification', {  })
        @end




</body>

</html>
