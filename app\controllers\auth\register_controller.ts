import User from '#models/user'
import { RegisterValidator } from '#validators/auth'
import type { HttpContext } from '@adonisjs/core/http'
import Mail from '@adonisjs/mail/services/main'
import cuid from 'cuid'
import router from '@adonisjs/core/services/router'
import {appurl} from '#config/app'


export default class RegisterController {
  async store({ request,route,view}: HttpContext) {
    // 1. Valider les données
    const userData = request.only(['email', 'password'])
    const validateData = await RegisterValidator.validate(userData)

    // 2. Génération token
    const token = cuid()

    // 3. Création utilisateur
    const user = await User.create({
      ...validateData,
      isVerified: false,
      verificationToken: token,
    })

    // 4. Générer le lien signé
    if (!route) {
      throw new Error('Route helper is not available')
    }
    const relativeUrl = await router
      .builder()
      .qs({ email: user.email, token })
      .makeSigned('auth.verifyEmail', { expiresIn: '30m' })

    const verificationUrl = `${appurl}${relativeUrl}`
    // 5. Envoyer l’email
    await Mail.send((message) => {
      message
        .from('<EMAIL>')
        .to(user.email)
        .subject('Vérifie ton adresse email')
        .htmlView('pages/VerifEmailTemplate/verify_email', { user, verificationUrl })
    })
    return view.render('pages/VerifEmailTemplate/ConsulterEmail', {email: user.email})
    
  }
}